<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="24dp"
    android:gravity="center"
    android:background="@color/white">

    <!-- App Logo/Title -->
    <ImageView
        android:layout_width="120dp"
        android:layout_height="120dp"
        android:layout_marginBottom="32dp"
        android:src="@drawable/ic_launcher_foreground"
        android:contentDescription="MtcInvoice Logo" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="MtcInvoice"
        android:textSize="28sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Please login to continue"
        android:textSize="16sp"
        android:textColor="@color/gray"
        android:layout_marginBottom="32dp" />

    <!-- Login Form -->
    <com.google.android.material.card.MaterialCardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="24dp"
        app:cardCornerRadius="12dp"
        app:cardElevation="4dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="24dp">

            <!-- Email Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:hint="Email"
                app:startIconDrawable="@drawable/ic_email"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextEmail"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textEmailAddress"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Password Input -->
            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="24dp"
                android:hint="Password"
                app:startIconDrawable="@drawable/ic_lock"
                app:endIconMode="password_toggle"
                style="@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/editTextPassword"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textPassword"
                    android:maxLines="1" />

            </com.google.android.material.textfield.TextInputLayout>

            <!-- Error Message -->
            <TextView
                android:id="@+id/textViewError"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:textColor="@color/red"
                android:textSize="14sp"
                android:visibility="gone"
                android:gravity="center" />

            <!-- Login Button -->
            <Button
                android:id="@+id/buttonLogin"
                android:layout_width="match_parent"
                android:layout_height="56dp"
                android:text="Login"
                android:textSize="16sp"
                android:textStyle="bold"
                style="@style/Widget.MaterialComponents.Button" />

            <!-- Progress Bar -->
            <ProgressBar
                android:id="@+id/progressBar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="16dp"
                android:visibility="gone" />

        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Default Credentials Info -->
    <TextView
        android:id="@+id/textViewDefaultCredentials"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Default credentials:\<EMAIL> / admin123\n(Tap to auto-fill)"
        android:textSize="12sp"
        android:textColor="@color/gray"
        android:gravity="center"
        android:background="?android:attr/selectableItemBackground"
        android:padding="8dp"
        android:layout_marginTop="16dp" />

</LinearLayout>
