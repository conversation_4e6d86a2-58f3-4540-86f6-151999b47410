<?php
/**
 * Admin File Upload Handler
 * Handles file uploads from the admin panel using session authentication
 * 
 * @package MtcInvoice Admin
 * @version 1.0
 */

require_once 'config/config.php';

// Require admin login
requireLogin();

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'error' => ['message' => 'Method not allowed']]);
    exit();
}

try {
    $current_user = getCurrentUser();
    
    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        throw new Exception('No file uploaded or upload error');
    }
    
    $file = $_FILES['file'];
    
    // File upload configuration (same as API)
    $max_file_size = 50 * 1024 * 1024; // 50MB
    $allowed_types = ['pdf', 'jpg', 'jpeg', 'png', 'doc', 'docx'];
    
    // Validate file size
    if ($file['size'] > $max_file_size) {
        throw new Exception('File size exceeds maximum allowed size (' . ($max_file_size / 1024 / 1024) . 'MB)');
    }
    
    // Get file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Validate file type
    if (!in_array($file_extension, $allowed_types)) {
        throw new Exception('File type not allowed. Allowed types: ' . implode(', ', $allowed_types));
    }
    
    // Create upload directory if it doesn't exist
    $upload_dir = '../uploads/pdfs/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $original_name = pathinfo($file['name'], PATHINFO_FILENAME);
    $timestamp = time();
    $unique_filename = $original_name . '_' . $timestamp . '.' . $file_extension;
    $file_path = $upload_dir . $unique_filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        throw new Exception('Failed to save uploaded file');
    }
    
    // Calculate file hash for deduplication
    $file_hash = hash_file('sha256', $file_path);
    
    // Generate unique ID (same function as API)
    function generatePushKey() {
        $chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz';
        $result = '';
        for ($i = 0; $i < 20; $i++) {
            $result .= $chars[mt_rand(0, strlen($chars) - 1)];
        }
        return $result;
    }
    
    // Prepare file info
    $file_info = [
        'id' => generatePushKey(),
        'original_name' => $file['name'],
        'stored_name' => $unique_filename,
        'file_path' => $file_path,
        'relative_path' => 'pdfs/' . $unique_filename,
        'file_size' => $file['size'],
        'file_type' => $file['type'],
        'file_extension' => $file_extension,
        'file_hash' => $file_hash,
        'uploaded_by' => $current_user['id'],
        'uploaded_at' => date('Y-m-d H:i:s'),
        'download_url' => ADMIN_URL . '../uploads/pdfs/' . urlencode($unique_filename)
    ];
    
    // Store file metadata in database
    try {
        $pdo = getDbConnection();
        
        // Check if file with same hash already exists for this user
        $stmt = $pdo->prepare("SELECT id FROM documents WHERE file_hash = ? AND user_id = ? AND deleted_at IS NULL");
        $stmt->execute([$file_hash, $current_user['id']]);
        
        if ($stmt->fetch()) {
            // File already exists, delete the uploaded duplicate
            unlink($file_path);
            throw new Exception('File already exists');
        }
        
        // Insert file metadata into documents table
        $stmt = $pdo->prepare("
            INSERT INTO documents (
                id, user_id, original_name, storage_path, file_name, 
                file_size, file_type, file_hash, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");
        
        $stmt->execute([
            $file_info['id'],
            $current_user['id'],
            $file_info['original_name'],
            $file_info['relative_path'],
            $file_info['stored_name'],
            $file_info['file_size'],
            $file_info['file_type'],
            $file_info['file_hash']
        ]);
        
        // Update storage usage
        $stmt = $pdo->prepare("
            INSERT INTO storage_usage (user_id, total_used, document_count) 
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE 
                total_used = total_used + ?,
                document_count = document_count + 1,
                last_updated = NOW()
        ");
        $stmt->execute([$current_user['id'], $file_info['file_size'], $file_info['file_size']]);
        
    } catch (PDOException $e) {
        // If database insertion fails, remove the uploaded file
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        error_log("Database error during file upload: " . $e->getMessage());
        throw new Exception('Failed to save file metadata');
    }
    
    // Log upload activity (simple logging)
    error_log("File uploaded by admin: " . $current_user['email'] . " - " . $file_info['original_name']);
    
    // Send success response
    echo json_encode([
        'success' => true,
        'data' => $file_info,
        'message' => 'File uploaded successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Admin file upload error: " . $e->getMessage());
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => [
            'message' => $e->getMessage()
        ]
    ]);
}
?>
