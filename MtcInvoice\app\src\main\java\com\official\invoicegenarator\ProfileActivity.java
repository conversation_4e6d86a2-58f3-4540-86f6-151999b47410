package com.official.invoicegenarator;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.google.android.material.appbar.MaterialToolbar;
import com.google.android.material.dialog.MaterialAlertDialogBuilder;
import com.official.invoicegenarator.auth.AuthManager;
import com.official.invoicegenarator.models.User;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.utils.UIThreadHelper;

/**
 * Profile Activity
 * Displays user profile information and provides logout functionality
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class ProfileActivity extends AppCompatActivity {
    
    private static final String TAG = "ProfileActivity";
    
    private TextView textUserName;
    private TextView textUserEmail;
    private TextView textUserRole;
    private TextView textLoginStatus;
    private Button buttonLogout;
    private Button buttonRefreshProfile;
    
    private AuthManager authManager;
    private ApiService apiService;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_profile);
        
        // Initialize components
        initializeViews();
        initializeServices();
        setupToolbar();
        setupClickListeners();
        
        // Load user profile
        loadUserProfile();
    }
    
    private void initializeViews() {
        textUserName = findViewById(R.id.textUserName);
        textUserEmail = findViewById(R.id.textUserEmail);
        textUserRole = findViewById(R.id.textUserRole);
        textLoginStatus = findViewById(R.id.textLoginStatus);
        buttonLogout = findViewById(R.id.buttonLogout);
        buttonRefreshProfile = findViewById(R.id.buttonRefreshProfile);
    }
    
    private void initializeServices() {
        authManager = new AuthManager(this);
        apiService = new ApiService();
    }
    
    private void setupToolbar() {
        MaterialToolbar toolbar = findViewById(R.id.toolbar);
        setSupportActionBar(toolbar);
        
        if (getSupportActionBar() != null) {
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
            getSupportActionBar().setTitle("Profile");
        }
        
        toolbar.setNavigationOnClickListener(v -> onBackPressed());
    }
    
    private void setupClickListeners() {
        buttonLogout.setOnClickListener(v -> showLogoutConfirmation());
        buttonRefreshProfile.setOnClickListener(v -> loadUserProfile());
    }
    
    private void loadUserProfile() {
        if (!authManager.isLoggedIn()) {
            UIThreadHelper.showToast(this, "Not logged in");
            redirectToLogin();
            return;
        }
        
        // Show loading state
        setLoadingState(true);
        
        // Get current user from stored data
        User currentUser = authManager.getCurrentUser();
        if (currentUser != null) {
            displayUserInfo(currentUser);
            setLoadingState(false);
        } else {
            // Try to verify token and get fresh user data
            apiService.verifyToken(new ApiCallback<User>() {
                @Override
                public void onSuccess(User user) {
                    UIThreadHelper.runOnUiThread(ProfileActivity.this, () -> {
                        displayUserInfo(user);
                        setLoadingState(false);
                    });
                }
                
                @Override
                public void onError(String error) {
                    UIThreadHelper.runOnUiThread(ProfileActivity.this, () -> {
                        setLoadingState(false);
                        UIThreadHelper.showToast(ProfileActivity.this, "Failed to load profile: " + error);
                        Log.e(TAG, "Profile load error: " + error);
                        
                        // If token verification fails, redirect to login
                        if (error.contains("Invalid") || error.contains("expired")) {
                            redirectToLogin();
                        }
                    });
                }
            });
        }
    }
    
    private void displayUserInfo(User user) {
        if (user != null) {
            textUserName.setText(user.getName() != null ? user.getName() : "Unknown");
            textUserEmail.setText(user.getEmail() != null ? user.getEmail() : "Unknown");
            textUserRole.setText(user.getRole() != null ? 
                user.getRole().substring(0, 1).toUpperCase() + user.getRole().substring(1) : "Unknown");
            textLoginStatus.setText("Logged In");
            textLoginStatus.setTextColor(getResources().getColor(android.R.color.holo_green_dark));
        } else {
            textUserName.setText("Unknown");
            textUserEmail.setText("Unknown");
            textUserRole.setText("Unknown");
            textLoginStatus.setText("Not Logged In");
            textLoginStatus.setTextColor(getResources().getColor(android.R.color.holo_red_dark));
        }
    }
    
    private void setLoadingState(boolean loading) {
        buttonRefreshProfile.setEnabled(!loading);
        buttonLogout.setEnabled(!loading);
        
        if (loading) {
            buttonRefreshProfile.setText("Loading...");
        } else {
            buttonRefreshProfile.setText("Refresh Profile");
        }
    }
    
    private void showLogoutConfirmation() {
        new MaterialAlertDialogBuilder(this)
                .setTitle("Logout")
                .setMessage("Are you sure you want to logout?")
                .setPositiveButton("Logout", (dialog, which) -> performLogout())
                .setNegativeButton("Cancel", (dialog, which) -> dialog.dismiss())
                .show();
    }
    
    private void performLogout() {
        try {
            // Clear authentication data
            authManager.logout();
            
            UIThreadHelper.showToast(this, "Logged out successfully");
            
            // Redirect to login
            redirectToLogin();
            
        } catch (Exception e) {
            Log.e(TAG, "Logout error: " + e.getMessage());
            UIThreadHelper.showToast(this, "Logout failed: " + e.getMessage());
        }
    }
    
    private void redirectToLogin() {
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
    @Override
    public void onBackPressed() {
        super.onBackPressed();
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // Refresh profile when returning to this activity
        loadUserProfile();
    }
}
