[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_dialog_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\dialog_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rounded_corners.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rounded_corners.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_table.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\table.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-anydpi-v24_downloadlist.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-anydpi-v24\\downloadlist.xml"}, {"merged": "com.official.invoicegenarator.app-debug-37:/layout_activity_login.xml.flat", "source": "com.official.invoicegenarator.app-main-39:/layout/activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_invoicelogo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\invoicelogo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-ldpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-ldpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_content_main_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\content_main_two.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_first_invoice.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\first_invoice.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-anydpi-v24_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-anydpi-v24\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\menu_text_to_number.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\menu\\text_to_number.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_empty_state.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\empty_state.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-hdpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-hdpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_texinfo.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\texinfo.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_footer_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\footer_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rounded.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rounded.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxxhdpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxxhdpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_divider.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\divider.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_update_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_update_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_remove_circle_outline_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_remove_circle_outline_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_btn_color_after.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\btn_color_after.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\font_timesnewromanbold.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\font\\timesnewromanbold.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_second_invoice.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\second_invoice.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rounded_corner.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rounded_corner.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_fingerprinttwo.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\fingerprinttwo.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_invoice_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_invoice_two.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_btn_bankr.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\btn_bankr.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_fingerprintsetting.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\fingerprintsetting.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_cell_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\cell_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_fingerprint_settings.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_fingerprint_settings.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\font_timesnewromanitalic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\font\\timesnewromanitalic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_ivtraker_item_row.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\ivtraker_item_row.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_fingerback.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\fingerback.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_custom_horizontal_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\custom_horizontal_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_card_view_elevation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\card_view_elevation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_income_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\income_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_invoice_traker.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\invoice_traker.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxhdpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxhdpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-hdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-hdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_progress.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_progress.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_btn_bankbackg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\btn_bankbackg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\xml\\backup_rules.xml"}, {"merged": "com.official.invoicegenarator.app-debug-37:/drawable_ic_lock.xml.flat", "source": "com.official.invoicegenarator.app-main-39:/drawable/ic_lock.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_chevron.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_chevron.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_co_present_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_co_present_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_loading_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\loading_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_chart.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_chart.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_border_textview.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\border_textview.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_menu_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_menu_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_money_bag.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_money_bag.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_update_delete_iv_traker.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\update_delete_iv_traker.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\font_timesnewroman.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\font\\timesnewroman.ttf"}, {"merged": "com.official.invoicegenarator.app-debug-37:/drawable_ic_email.xml.flat", "source": "com.official.invoicegenarator.app-main-39:/drawable/ic_email.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxhdpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxhdpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxhdpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxhdpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\anim_fade_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\anim\\fade_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_accounts_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\accounts_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_custom_file_name_dialog.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\custom_file_name_dialog.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_tround.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\tround.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_add_expense.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_add_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_loan.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_loan.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-anydpi-v24_ic_plus.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-anydpi-v24\\ic_plus.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xhdpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xhdpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_item_income.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\item_income.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_navigationicon.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\navigationicon.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_varify.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_varify.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_vertical_border_black.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\vertical_border_black.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\anim_rotate_in.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\anim\\rotate_in.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xhdpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xhdpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\raw_click_sound.mp3.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\raw\\click_sound.mp3"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_more.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_more.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_fragment_update_delete.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\fragment_update_delete.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_default_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\default_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_fingerprintthree.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\fingerprintthree.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxxhdpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxxhdpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-ldpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-ldpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_nav_header.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\nav_header.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rounded_search_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rounded_search_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_fingerprint.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\fingerprint.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-hdpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-hdpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xhdpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xhdpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_iv_traker_upload.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\iv_traker_upload.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_settings_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_settings_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-anydpi-v24_ic_accounts.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-anydpi-v24\\ic_accounts.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_signature.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\signature.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_border_black.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\border_black.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_fragment_expense.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\fragment_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_delete.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\delete.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_alert_dialog_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\alert_dialog_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_button_selector_effect.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\button_selector_effect.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_custom_vertical_thumb.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\custom_vertical_thumb.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_pdf_viewer.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_pdf_viewer.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_add_income.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_add_income.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-hdpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-hdpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_mtcsplash.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\mtcsplash.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_star.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_star.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_img.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\img.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_underline.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\underline.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_converter_document.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\converter_document.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-anydpi-v24_ic_search.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-anydpi-v24\\ic_search.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_salary.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_salary.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_item_data_invoice_traker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\item_data_invoice_traker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_fgdata_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\fgdata_upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\font_timesnewromanbolditalic.ttf.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\font\\timesnewromanbolditalic.ttf"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-mdpi_ic_plus.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-mdpi\\ic_plus.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\menu_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\menu\\nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-ldpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-ldpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_workerattendance.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\workerattendance.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_download_list.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_download_list.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_wallet_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_wallet_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_other.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_other.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_banklay.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\banklay.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xhdpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xhdpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_number_to_words.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_number_to_words.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_background_curve_right.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\background_curve_right.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_save_pdf.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_save_pdf.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_settings_backup_restore_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_settings_backup_restore_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_item_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\item_two.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_invoice.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\invoice.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_texinfo_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\texinfo_two.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ltbback.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ltbback.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-ldpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-ldpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_investment.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_investment.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_download.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\download.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_expense_selector.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\expense_selector.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_fragment_data_upload.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\fragment_data_upload.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxxhdpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxxhdpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-hdpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-hdpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rtbback.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rtbback.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\anim_rotate_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\anim\\rotate_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\raw_loading.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\raw\\loading.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-xxxhdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-xxxhdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_view.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\view.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_category_bg.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\category_bg.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_signature_layout.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\signature_layout.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_document_scanner_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_document_scanner_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_pdf_item.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\pdf_item.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_book.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_book.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_rightbgadd.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\rightbgadd.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-mdpi_ic_launcher_foreground.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-mdpi\\ic_launcher_foreground.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_baseline_add_circle_outline_24.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\baseline_add_circle_outline_24.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_worker_presentation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_worker_presentation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_moneybag.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\moneybag.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_content_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\content_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_texmain.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\texmain.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_item_expense.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\item_expense.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_dialog_exit_confirmation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\dialog_exit_confirmation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_header_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\header_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-mdpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-mdpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-mdpi_ic_accounts.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-mdpi\\ic_accounts.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_table_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\table_two.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_invoice_traker.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_invoice_traker.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_rent.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_rent.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxhdpi_downloadlist.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxhdpi\\downloadlist.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_selection.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_selection.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_backgroundwttop.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\backgroundwttop.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_ic_business.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\ic_business.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\anim_fade_out.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\anim\\fade_out.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_fragment_income.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\fragment_income.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-mdpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-mdpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable_btn_color_before.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\drawable\\btn_color_before.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\drawable-xxxhdpi_ic_search.png.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-pngs-33:\\drawable-xxxhdpi\\ic_search.png"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-debug-37:\\layout_texmain_two.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.14.2\\com.official.invoicegenarator.app-main-39:\\layout\\texmain_two.xml"}]