<?php
/**
 * File Upload API
 * Replaces Firebase Storage functionality
 * 
 * @package MtcInvoice
 * @version 1.0
 */

require_once '../config/config.php';
require_once '../models/User.php';
require_once '../utils/jwt.php';

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendErrorResponse('Method not allowed', 405);
}

try {
    // Authentication required
    $user = JWT::requireAuth(JWT_SECRET_KEY);
    
    // Check if file was uploaded
    if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
        sendErrorResponse('No file uploaded or upload error', 400);
    }
    
    $file = $_FILES['file'];
    
    // Validate file size
    if ($file['size'] > MAX_FILE_SIZE) {
        sendErrorResponse('File size exceeds maximum allowed size (' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB)', 400);
    }
    
    // Get file extension
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Validate file type
    if (!in_array($file_extension, ALLOWED_FILE_TYPES)) {
        sendErrorResponse('File type not allowed. Allowed types: ' . implode(', ', ALLOWED_FILE_TYPES), 400);
    }
    
    // Create upload directory if it doesn't exist
    $upload_dir = UPLOAD_DIR . 'pdfs/';
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $original_name = pathinfo($file['name'], PATHINFO_FILENAME);
    $timestamp = time();
    $unique_filename = $original_name . '_' . $timestamp . '.' . $file_extension;
    $file_path = $upload_dir . $unique_filename;
    
    // Move uploaded file
    if (!move_uploaded_file($file['tmp_name'], $file_path)) {
        sendErrorResponse('Failed to save uploaded file', 500);
    }
    
    // Calculate file hash for deduplication
    $file_hash = hash_file('sha256', $file_path);
    
    // Get file info
    $file_info = [
        'id' => generatePushKey(),
        'original_name' => $file['name'],
        'stored_name' => $unique_filename,
        'file_path' => $file_path,
        'relative_path' => 'pdfs/' . $unique_filename,
        'file_size' => $file['size'],
        'file_type' => $file['type'],
        'file_extension' => $file_extension,
        'file_hash' => $file_hash,
        'uploaded_by' => $user['user_id'],
        'uploaded_at' => date('Y-m-d H:i:s'),
        'download_url' => BASE_URL . 'files/download.php?file=' . urlencode($unique_filename)
    ];

    // Store file metadata in database
    try {
        $pdo = getDbConnection();

        // Check if file with same hash already exists for this user
        $stmt = $pdo->prepare("SELECT id FROM documents WHERE file_hash = ? AND user_id = ? AND deleted_at IS NULL");
        $stmt->execute([$file_hash, $user['user_id']]);

        if ($stmt->fetch()) {
            // File already exists, delete the uploaded duplicate
            unlink($file_path);
            sendErrorResponse('File already exists', 409);
        }

        // Insert file metadata into documents table
        $stmt = $pdo->prepare("
            INSERT INTO documents (
                id, user_id, original_name, storage_path, file_name,
                file_size, file_type, file_hash, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
        ");

        $stmt->execute([
            $file_info['id'],
            $user['user_id'],
            $file_info['original_name'],
            $file_info['relative_path'],
            $file_info['stored_name'],
            $file_info['file_size'],
            $file_info['file_type'],
            $file_info['file_hash']
        ]);

        // Update storage usage
        $stmt = $pdo->prepare("
            INSERT INTO storage_usage (user_id, total_used, document_count)
            VALUES (?, ?, 1)
            ON DUPLICATE KEY UPDATE
                total_used = total_used + ?,
                document_count = document_count + 1,
                last_updated = NOW()
        ");
        $stmt->execute([$user['user_id'], $file_info['file_size'], $file_info['file_size']]);

    } catch (PDOException $e) {
        // If database insertion fails, remove the uploaded file
        if (file_exists($file_path)) {
            unlink($file_path);
        }
        error_log("Database error during file upload: " . $e->getMessage());
        sendErrorResponse('Failed to save file metadata', 500);
    }

    // Log upload activity
    logActivity('file_uploaded', [
        'file_id' => $file_info['id'],
        'original_name' => $file_info['original_name'],
        'file_size' => $file_info['file_size'],
        'user_id' => $user['user_id']
    ]);

    sendSuccessResponse($file_info, 'File uploaded successfully');
    
} catch (Exception $e) {
    error_log("File upload error: " . $e->getMessage());
    sendErrorResponse('File upload failed', 500);
}
