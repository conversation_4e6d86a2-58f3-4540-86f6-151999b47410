<?php
/**
 * Test Authentication API
 */

echo "<h2>Authentication API Test</h2>";

// Test login endpoint
echo "<h3>Testing Login API</h3>";

$login_data = json_encode([
    'email' => '<EMAIL>',
    'password' => 'admin123'
]);

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, 'http://192.168.0.106/MtcInvoiceMasudvi/api/auth/login.php');
curl_setopt($ch, CURLOPT_POST, 1);
curl_setopt($ch, CURLOPT_POSTFIELDS, $login_data);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($login_data)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

$response = curl_exec($ch);
$http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "<p><strong>HTTP Status:</strong> $http_code</p>";
echo "<p><strong>Response:</strong></p>";
echo "<pre>" . htmlspecialchars($response) . "</pre>";

if ($http_code == 200) {
    $login_response = json_decode($response, true);
    if ($login_response && isset($login_response['data']['token'])) {
        $token = $login_response['data']['token'];
        echo "<p style='color: green;'>✓ Login successful! Token obtained.</p>";
        
        // Test file upload with token
        echo "<h3>Testing File Upload with Authentication</h3>";
        
        // Create a test file
        $test_content = "This is a test file for authentication testing.";
        $temp_file = tempnam(sys_get_temp_dir(), 'auth_test_');
        file_put_contents($temp_file, $test_content);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://192.168.0.106/MtcInvoiceMasudvi/api/files/upload.php');
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, [
            'file' => new CURLFile($temp_file, 'text/plain', 'test.txt')
        ]);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $upload_response = curl_exec($ch);
        $upload_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>Upload HTTP Status:</strong> $upload_http_code</p>";
        echo "<p><strong>Upload Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($upload_response) . "</pre>";
        
        if ($upload_http_code == 200) {
            echo "<p style='color: green;'>✓ File upload with authentication successful!</p>";
        } else {
            echo "<p style='color: red;'>✗ File upload failed even with authentication</p>";
        }
        
        // Clean up
        unlink($temp_file);
        
        // Test token verification
        echo "<h3>Testing Token Verification</h3>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, 'http://192.168.0.106/MtcInvoiceMasudvi/api/auth/verify.php');
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . $token
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $verify_response = curl_exec($ch);
        $verify_http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "<p><strong>Verify HTTP Status:</strong> $verify_http_code</p>";
        echo "<p><strong>Verify Response:</strong></p>";
        echo "<pre>" . htmlspecialchars($verify_response) . "</pre>";
        
        if ($verify_http_code == 200) {
            echo "<p style='color: green;'>✓ Token verification successful!</p>";
        } else {
            echo "<p style='color: red;'>✗ Token verification failed</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ Login response doesn't contain token</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Login failed</p>";
}

echo "<hr>";
echo "<p><a href='admin/login.php'>Test Admin Panel Login</a></p>";
echo "<p><a href='api/setup/install.php'>Check Database Setup</a></p>";
?>
