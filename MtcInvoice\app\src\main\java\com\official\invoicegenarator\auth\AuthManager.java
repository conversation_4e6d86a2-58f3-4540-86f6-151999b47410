package com.official.invoicegenarator.auth;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;

import com.official.invoicegenarator.network.ApiService;
import com.official.invoicegenarator.network.ApiCallback;
import com.official.invoicegenarator.network.ApiClient;
import com.official.invoicegenarator.models.LoginResponse;
import com.official.invoicegenarator.models.User;

/**
 * Authentication Manager for MtcInvoice App
 * Handles login, logout, token storage, and authentication state
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class AuthManager {
    private static final String TAG = "AuthManager";
    private static final String PREFS_NAME = "MtcInvoiceAuth";
    private static final String KEY_AUTH_TOKEN = "auth_token";
    private static final String KEY_USER_ID = "user_id";
    private static final String KEY_USER_NAME = "user_name";
    private static final String KEY_USER_EMAIL = "user_email";
    private static final String KEY_USER_ROLE = "user_role";
    private static final String KEY_TOKEN_EXPIRY = "token_expiry";
    
    private static AuthManager instance;
    private Context context;
    private SharedPreferences prefs;
    private ApiService apiService;
    private ApiClient apiClient;
    
    private AuthManager(Context context) {
        this.context = context.getApplicationContext();
        this.prefs = this.context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.apiService = new ApiService();
        this.apiClient = ApiClient.getInstance();
        
        // Restore token if available
        restoreAuthToken();
    }
    
    public static synchronized AuthManager getInstance(Context context) {
        if (instance == null) {
            instance = new AuthManager(context);
        }
        return instance;
    }
    
    /**
     * Login with email and password
     */
    public void login(String email, String password, AuthCallback callback) {
        apiService.login(email, password, new ApiCallback<LoginResponse>() {
            @Override
            public void onSuccess(LoginResponse loginResponse) {
                // Store authentication data
                storeAuthData(loginResponse);
                
                Log.d(TAG, "Login successful for user: " + email);
                callback.onSuccess(loginResponse.getUser());
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "Login failed: " + error);
                callback.onError(error);
            }
        });
    }
    
    /**
     * Logout user and clear stored data
     */
    public void logout() {
        // Clear stored authentication data
        SharedPreferences.Editor editor = prefs.edit();
        editor.clear();
        editor.apply();
        
        // Clear token from API client
        apiClient.clearAuthToken();
        
        Log.d(TAG, "User logged out successfully");
    }
    
    /**
     * Check if user is currently logged in
     */
    public boolean isLoggedIn() {
        String token = prefs.getString(KEY_AUTH_TOKEN, null);
        long expiry = prefs.getLong(KEY_TOKEN_EXPIRY, 0);
        
        if (token == null || token.isEmpty()) {
            return false;
        }
        
        // Check if token is expired
        if (System.currentTimeMillis() > expiry) {
            Log.d(TAG, "Token expired, logging out");
            logout();
            return false;
        }
        
        return true;
    }
    
    /**
     * Get current authentication token
     */
    public String getAuthToken() {
        if (isLoggedIn()) {
            return prefs.getString(KEY_AUTH_TOKEN, null);
        }
        return null;
    }
    
    /**
     * Get current user information
     */
    public User getCurrentUser() {
        if (!isLoggedIn()) {
            return null;
        }
        
        User user = new User();
        user.setId(prefs.getInt(KEY_USER_ID, 0));
        user.setName(prefs.getString(KEY_USER_NAME, ""));
        user.setEmail(prefs.getString(KEY_USER_EMAIL, ""));
        user.setRole(prefs.getString(KEY_USER_ROLE, ""));
        
        return user;
    }
    
    /**
     * Verify current token with server
     */
    public void verifyToken(AuthCallback callback) {
        if (!isLoggedIn()) {
            callback.onError("No valid token available");
            return;
        }
        
        apiService.verifyToken(new ApiCallback<User>() {
            @Override
            public void onSuccess(User user) {
                Log.d(TAG, "Token verification successful");
                callback.onSuccess(user);
            }
            
            @Override
            public void onError(String error) {
                Log.e(TAG, "Token verification failed: " + error);
                // Token is invalid, logout user
                logout();
                callback.onError(error);
            }
        });
    }
    
    /**
     * Store authentication data after successful login
     */
    private void storeAuthData(LoginResponse loginResponse) {
        SharedPreferences.Editor editor = prefs.edit();
        
        // Store token
        editor.putString(KEY_AUTH_TOKEN, loginResponse.getToken());
        
        // Store user data
        User user = loginResponse.getUser();
        if (user != null) {
            editor.putInt(KEY_USER_ID, user.getId());
            editor.putString(KEY_USER_NAME, user.getName());
            editor.putString(KEY_USER_EMAIL, user.getEmail());
            editor.putString(KEY_USER_ROLE, user.getRole());
        }
        
        // Calculate and store expiry time (token expires in seconds, convert to milliseconds)
        long expiryTime = System.currentTimeMillis() + (loginResponse.getExpiresIn() * 1000L);
        editor.putLong(KEY_TOKEN_EXPIRY, expiryTime);
        
        editor.apply();
        
        // Set token in API client
        apiClient.setAuthToken(loginResponse.getToken());
        
        Log.d(TAG, "Authentication data stored successfully");
    }
    
    /**
     * Restore authentication token from storage
     */
    private void restoreAuthToken() {
        if (isLoggedIn()) {
            String token = prefs.getString(KEY_AUTH_TOKEN, null);
            if (token != null) {
                apiClient.setAuthToken(token);
                Log.d(TAG, "Authentication token restored");
            }
        }
    }
    
    /**
     * Callback interface for authentication operations
     */
    public interface AuthCallback {
        void onSuccess(User user);
        void onError(String error);
    }
}
