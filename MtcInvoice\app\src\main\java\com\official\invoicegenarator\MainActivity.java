package com.official.invoicegenarator;

import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.os.Handler;
import android.view.WindowManager;
import androidx.appcompat.app.AppCompatActivity;

import com.official.invoicegenarator.auth.AuthManager;

public class MainActivity extends AppCompatActivity {

    private static final String PREFS_NAME = "FingerprintPrefs";
    private static final String KEY_FINGERPRINT_ENABLED = "fingerprintEnabled";

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                WindowManager.LayoutParams.FLAG_FULLSCREEN);
        setContentView(R.layout.activity_main);

        new Handler().postDelayed(new Runnable() {
            @Override
            public void run() {
                // Check authentication status first
                AuthManager authManager = AuthManager.getInstance(MainActivity.this);

                Intent intent;
                if (!authManager.isLoggedIn()) {
                    // User not authenticated, go to login
                    intent = new Intent(MainActivity.this, LoginActivity.class);
                } else {
                    // User is authenticated, check fingerprint preference
                    SharedPreferences prefs = getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
                    boolean isFingerprintEnabled = prefs.getBoolean(KEY_FINGERPRINT_ENABLED, false);

                    if (isFingerprintEnabled) {
                        // If fingerprint authentication is enabled, go to VerifyActivity
                        intent = new Intent(MainActivity.this, VarifyActivity.class);
                    } else {
                        // If fingerprint authentication is disabled, go to SelectionActivity
                        intent = new Intent(MainActivity.this, SelectionActivity.class);
                    }
                }

                startActivity(intent);
                finish();
            }
        }, 2000);
    }
}
