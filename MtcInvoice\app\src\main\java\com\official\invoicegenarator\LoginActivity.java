package com.official.invoicegenarator;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.appcompat.app.AppCompatActivity;

import com.official.invoicegenarator.auth.AuthManager;
import com.official.invoicegenarator.models.User;
import com.official.invoicegenarator.utils.UIThreadHelper;

/**
 * Login Activity for MtcInvoice App
 * Handles user authentication before accessing main features
 * 
 * <AUTHOR> Team
 * @version 1.0
 */
public class LoginActivity extends AppCompatActivity {
    private static final String TAG = "LoginActivity";
    
    private EditText editTextEmail;
    private EditText editTextPassword;
    private Button buttonLogin;
    private ProgressBar progressBar;
    private TextView textViewError;
    private TextView textViewDefaultCredentials;
    
    private AuthManager authManager;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);
        
        // Initialize AuthManager
        authManager = AuthManager.getInstance(this);
        
        // Check if user is already logged in
        if (authManager.isLoggedIn()) {
            navigateToMainApp();
            return;
        }
        
        initializeViews();
        setupClickListeners();
        showDefaultCredentials();
    }
    
    private void initializeViews() {
        editTextEmail = findViewById(R.id.editTextEmail);
        editTextPassword = findViewById(R.id.editTextPassword);
        buttonLogin = findViewById(R.id.buttonLogin);
        progressBar = findViewById(R.id.progressBar);
        textViewError = findViewById(R.id.textViewError);
        textViewDefaultCredentials = findViewById(R.id.textViewDefaultCredentials);
        
        // Hide progress bar initially
        progressBar.setVisibility(View.GONE);
        textViewError.setVisibility(View.GONE);
    }
    
    private void setupClickListeners() {
        buttonLogin.setOnClickListener(v -> attemptLogin());
        
        // Allow clicking on default credentials to auto-fill
        textViewDefaultCredentials.setOnClickListener(v -> {
            editTextEmail.setText("<EMAIL>");
            editTextPassword.setText("admin123");
        });
    }
    
    private void showDefaultCredentials() {
        textViewDefaultCredentials.setText("Default credentials:\<EMAIL> / admin123\n(Tap to auto-fill)");
        textViewDefaultCredentials.setVisibility(View.VISIBLE);
    }
    
    private void attemptLogin() {
        String email = editTextEmail.getText().toString().trim();
        String password = editTextPassword.getText().toString().trim();
        
        // Validate input
        if (email.isEmpty()) {
            showError("Please enter your email");
            editTextEmail.requestFocus();
            return;
        }
        
        if (password.isEmpty()) {
            showError("Please enter your password");
            editTextPassword.requestFocus();
            return;
        }
        
        // Show loading state
        setLoadingState(true);
        hideError();
        
        // Attempt login
        authManager.login(email, password, new AuthManager.AuthCallback() {
            @Override
            public void onSuccess(User user) {
                UIThreadHelper.runOnUiThread(LoginActivity.this, () -> {
                    setLoadingState(false);
                    UIThreadHelper.showToast(LoginActivity.this, "Login successful! Welcome " + user.getName());
                    navigateToMainApp();
                });
            }
            
            @Override
            public void onError(String error) {
                UIThreadHelper.runOnUiThread(LoginActivity.this, () -> {
                    setLoadingState(false);
                    showError("Login failed: " + error);
                });
            }
        });
    }
    
    private void setLoadingState(boolean loading) {
        if (loading) {
            progressBar.setVisibility(View.VISIBLE);
            buttonLogin.setEnabled(false);
            buttonLogin.setText("Logging in...");
            editTextEmail.setEnabled(false);
            editTextPassword.setEnabled(false);
        } else {
            progressBar.setVisibility(View.GONE);
            buttonLogin.setEnabled(true);
            buttonLogin.setText("Login");
            editTextEmail.setEnabled(true);
            editTextPassword.setEnabled(true);
        }
    }
    
    private void showError(String message) {
        textViewError.setText(message);
        textViewError.setVisibility(View.VISIBLE);
    }
    
    private void hideError() {
        textViewError.setVisibility(View.GONE);
    }
    
    private void navigateToMainApp() {
        Intent intent = new Intent(LoginActivity.this, SelectionActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }
    
    @Override
    public void onBackPressed() {
        // Prevent going back from login screen
        finishAffinity();
    }
}
